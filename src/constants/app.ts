type AppName = 'comatestack' | 'icode' | 'ievalue' | 'iplayground' | 'mcp' | 'external';

export const EXTERNAL_HOST_NAME = 'comatestack.baidu.com';
const EXTERNAL_HOST_NAMES = ['comatestackqa.baidu.com', EXTERNAL_HOST_NAME];

const getAppName = (): AppName => {
    const pathname = window.location.pathname;
    const hostname = window.location.hostname;

    if (EXTERNAL_HOST_NAMES.includes(hostname)) {
        return 'external';
    }
    if (pathname.startsWith('/comatestack/mcp')) {
        return 'mcp';
    }
    if (pathname.startsWith('/comatestack')) {
        return 'comatestack';
    }
    if (pathname.startsWith('/devops/icode')) {
        return 'icode';
    }
    if (pathname.startsWith('/devops/ievalue')) {
        return 'ievalue';
    }
    if (pathname.startsWith('/devops/iplayground')) {
        return 'iplayground';
    }
    return 'comatestack';
};

export const APP_NAME = getAppName();

export const APP_IS_EXTERNAL = APP_NAME === 'external';

export const APP_POWERED_BY_QIANKUN = window.__POWERED_BY_QIANKUN__;

export const APP_IS_ONLINE_PRODUCTION = [
    'console.cloud.baidu-int.com',
    'iapi.baidu-int.com',
].includes(window.location.hostname);

export const APP_IS_ICLOUD = [
    'console.cloud.baidu-int.com',
    'console.cloud-sandbox.baidu-int.com',
].includes(window.location.hostname);

export const APP_IS_DEV = window.location.hostname === 'localhost';

export const COMATESTACK_BASENAME = '/comatestack';

export const ICODE_BASENAME = '/devops/icode';

export const IEVALUE_BASENAME = '/devops/ievalue';

export const IPLAYGROUND_BASENAME = '/devops/iplayground';

export const ICAFE_BASENAME = '/devops/icafe';

export const IPLAYGROUND_API_PREFIX = '/api/iplayground';

export const IEVALUE_API_PREFIX = '/api/ievalue';

const getAppBasename = () => {
    switch (APP_NAME) {
        case 'comatestack':
            return COMATESTACK_BASENAME;
        case 'icode':
            return ICODE_BASENAME;
        case 'ievalue':
            return IEVALUE_BASENAME;
        case 'iplayground':
            return IPLAYGROUND_BASENAME;
        default:
            return COMATESTACK_BASENAME;
    }
};

/** 这个值随着不同环境改变，谨慎使用 */
export const APP_BASENAME = getAppBasename();

/** 只有上线后才有值，谨慎使用 */
export const APP_ICLOUD_USERNAME = window?.__icloud__?.username ?? '';

export const APP_IS_IN_IFRAME = window.self !== window.top;

const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
const host = window.location.host;
export const APP_WEBSOCKET_PREFIX = `${protocol}://${host}`;

export const APP_HEADER_HEIGHT = '48px';

export const PASSPORT_LOGIN_WRAPPER_JS = `https://${window.location.hostname === EXTERNAL_HOST_NAME ? 'passport.baidu.com' : 'passport.qatest.baidu.com'}/passApi/js/uni_login_wrapper.js`;
