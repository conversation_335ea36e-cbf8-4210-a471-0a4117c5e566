import {random} from 'lodash';
import createFactory from '@baidu/devops-track';
import {APP_ICLOUD_USERNAME, APP_IS_ONLINE_PRODUCTION} from '@/constants/app';

const {createTrack} = createFactory({
    app: 'comatestack',
    username: APP_ICLOUD_USERNAME,
    isProduction: APP_IS_ONLINE_PRODUCTION,
});

export const trackPageView = createTrack('页面PV');

export const trackUnexpectError = createTrack('未知错误');

export const trackAction = createTrack('用户行为');

type TrackActionError = (label: `${string}：失败`, extraInfo?: unknown) => void;

export const trackError: TrackActionError = createTrack('错误');

export const newTraceID = () => {
    const timestamp = Date.now();
    const randomNumber = random(1e9, 1e10 - 1);
    const id = timestamp.toString(36).padStart(9, '0') + randomNumber.toString(36).padStart(7, '0');
    return id.toUpperCase();
};


const {createTrack: createMCPTrack} = createFactory({
    app: 'mcp',
    username: APP_ICLOUD_USERNAME,
    isProduction: APP_IS_ONLINE_PRODUCTION,
});

export const trackMCPPageView = createMCPTrack('页面PV');
export const trackMCPAction = createMCPTrack('用户行为');
