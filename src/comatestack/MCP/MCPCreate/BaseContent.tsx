import {Form} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {useSystemVars, useAuthTypeHandler} from '../shared/hooks';
import DescriptionField from '../MCPEdit/BasicInfoContent/DescriptionField';
import ProtocolField from '../MCPEdit/BasicInfoContent/ProtocolField';
import SceneField from '../MCPEdit/BasicInfoContent/SceneField';
import ServerConfigField from '../MCPEdit/BasicInfoContent/ServerConfigField';
import EnglishIdentifierField from './EnglishIdentifierField';
import GlobalVariableField from './GlobalVariableField';
import MCPIdentifierField from './MCPIdentifierField';
import ServiceNameField from './ServiceNameField';
import Overview from './Overview';
import AuthDescription from './OpenApiFields/AuthDescriptionField';
import AuthTypeField from './OpenApiFields/AuthTypeField';
import <PERSON><PERSON><PERSON><PERSON>ield from './MCPSpaceField';

interface Props {
    mode: string;
    hidden?: boolean;
}

const BaseContent = ({mode, hidden}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const form = Form.useFormInstance();
    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);

    const systemVars = useSystemVars({
        mode,
        authType,
    });

    const handleAuthTypeChange = useAuthTypeHandler({
        form,
        serverParamsPath: ['serverParams'],
    });


    return (
        <div style={{display: hidden ? 'none' : 'block'}}>
            <ServiceNameField />
            {!spaceId && <MCPSpaceField />}
            <EnglishIdentifierField />
            <MCPIdentifierField />
            <DescriptionField />
            {/* <VisibilityField /> */}
            <SceneField />
            <ProtocolField />
            {mode === 'openapi' && (
                <>
                    <AuthTypeField onAuthTypeChange={handleAuthTypeChange} />
                    <AuthDescription />
                </>
            )}
            {(mode === 'openapi' || mode === 'script') && (
                <Form.Item label="全局变量" name={['serverParams']}>
                    <GlobalVariableField
                        path={['serverParams']}
                        systemVars={systemVars}
                    />
                </Form.Item>
            )}
            {
                mode === 'external' && (
                    <>
                        <ServerConfigField />
                    </>
                )
            }
            <Overview />
        </div>
    );
};

export default BaseContent;
