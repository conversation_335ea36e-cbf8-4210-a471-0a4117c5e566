/* eslint-disable max-lines */
import {Form} from 'antd';
import {keys} from 'lodash';
import {useCallback, useMemo} from 'react';
import {message} from '@panda-design/components';
import {useSearchParams} from '@panda-design/router';
import {BaseParam} from '@/types/mcp/mcp';
import {apiPostMCPTool, apiPutMCPTool} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServerToolItem} from '@/regions/mcp/mcpServer';
import {useSearchReplace} from '@/hooks/icode/common/useSearchParams';
import {resetTouchedBasePath} from '../regions';
import {fillParamValueToJSONProperties, getBodySchemaTreeData, TreeData} from '../utils';
import {useMCPEditFormValidationListener} from '../Providers/MCPEditFormValidationInteractionProvider';
import {errorRowCss} from '../styles';

export const useActiveTool = () => {
    const {activeToolIndex} = useSearchParams();
    const replace = useSearchReplace();
    const setActiveToolIndex = useCallback(
        (index: number) => {
            replace({activeToolIndex: index});
        },
        [replace]
    );
    return {activeToolIndex: Number(activeToolIndex || -1), setActiveToolIndex};
};

interface Props {
    canSelectRoot?: boolean;
    titleWidthRoot?: boolean;
}

export const useHandleRelateParamTreeData = ({canSelectRoot = true, titleWidthRoot}: Props): TreeData[] => {
    const {activeToolIndex} = useActiveTool();
    const paramConfigs = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters']);
    const handleTrees = useMemo(
        () => {
            const roots = keys(paramConfigs);
            const data = roots
                .filter(root => paramConfigs?.[root]?.length > 0)
                .map(root => ({
                    title: root,
                    value: root,
                    disabled: !canSelectRoot,
                    key: root,
                    children:
                        root === 'body'
                            ? getBodySchemaTreeData(paramConfigs?.[root], titleWidthRoot)
                            : paramConfigs?.[root].map((item: any) => ({
                                title: titleWidthRoot ? `${root}.${item?.name}` : item?.name,
                                value: item.key,
                                key: item.key,
                            })),
                }));
            return data;
        },
        [canSelectRoot, paramConfigs, titleWidthRoot]
    );
    return handleTrees;
};

interface SaveToolProps {
    on: () => void;
    off: () => void;
    onSuccess?: () => void;
}

interface ApiParam extends BaseParam {
    canRelate: boolean;
    children?: ApiParam[];
}

interface ApiParams {
    body?: ApiParam[];
    cookie?: ApiParam[];
    header?: ApiParam[];
    path?: ApiParam[];
    query?: ApiParam[];
}

const validateRequiredParams = (
    paramsList: BaseParam[],
    apiParams?: ApiParam[]
): ApiParam | undefined => {
    if (!apiParams) {
        return;
    }
    const invalidItem = apiParams.find(item => {
        const inParamsList = paramsList.find(param => param.key === item.key);
        if (item.required && !inParamsList && item.canRelate && !item.value) {
            return true;
        }
        return false;
    });
    return invalidItem;
};


// flatten a tree to a list
const flattenBodyParams = (tree: ApiParam[]): ApiParam[] => {
    const result: ApiParam[] = [];
    tree.forEach(node => {
        result.push(node);
        if (node.children && node.dataType !== 'array') {
            result.push(...flattenBodyParams(node.children));
        }
    });
    return result;
};

const validateBodyParams = (paramsList: BaseParam[], bodyParams?: ApiParam[]): ApiParam => {
    if (!bodyParams) {
        return;
    }
    const flattenParams = flattenBodyParams(bodyParams);
    const validation = validateRequiredParams(paramsList, flattenParams);
    return validation;
};

const validateToolParams = (paramsList: BaseParam[], apiParams: ApiParams): ApiParam | undefined => {
    const {body, cookie, header, path, query} = apiParams;
    const validation = validateRequiredParams(paramsList, cookie)
        || validateRequiredParams(paramsList, header)
        || validateRequiredParams(paramsList, path)
        || validateRequiredParams(paramsList, query)
        || validateBodyParams(paramsList, body);
    return validation;
};

export class ApiParamsValidationError extends Error {
    errorParam: ApiParam;
    constructor(param: ApiParam) {
        super(`请填写参数${param?.key}的默认值，或关联至工具中`);
        this.name = 'ApiParamsValidationError';
        this.errorParam = param;
    }
}

export const useHandleSaveTool = ({on, off, onSuccess}: SaveToolProps): (() => Promise<void>) => {
    const {validateFields, setFieldValue} = Form.useFormInstance();
    const mcpServerId = useMCPServerId();
    const {activeToolIndex} = useActiveTool();
    const {publishValidationError} = useMCPEditFormValidationListener();
    const handleValidationError = useCallback(
        (e: any) => {
            if (e instanceof ApiParamsValidationError) {
                // 有可能需要切换到有未填内容的tab
                message.warning(e.message);
                publishValidationError(e);
            } else {
                const toolParamsRows = document.querySelectorAll('.toolParamsTableRow');
                e?.errorFields?.forEach((field: any) => {
                    if (field.name?.join('.')?.includes('toolParams.toolParams')) {
                        const toolParamIndex = field.name[field.name.length - 2];
                        const errorRow = toolParamsRows[toolParamIndex];
                        if (errorRow) {
                            errorRow.classList.add(errorRowCss);
                        }
                    }
                });
                message.warning('请检查表单信息');
            }
        },
        [publishValidationError]
    );
    const bundleParams = useCallback(
        async () => {
            // TODO 不同MCP的tool内容是不一样的，字段也有差异，最好能根据MCP的类型来拆一下。目前是混在一起的。
            const values = await validateFields();
            const {tools, serverSourceType} = values;
            const toolItem = tools?.[activeToolIndex];
            const {toolConf, id, toolParams, ...rest} = toolItem;
            const {parameters = {}, requestBody, ...otherOpenapiConf} = toolConf?.openapiConf ?? {};
            const {body, ...otherParameters} = parameters;
            const newRequestBody = {
                ...requestBody,
                jsonSchema: fillParamValueToJSONProperties(body, requestBody?.jsonSchema),
            };
            /**
             * 虽然通过form实例上的validateFields可以进行校验，但是有些表单项因为tab切换被隐藏了，导致越过了校验。
             * 所以需手动校验：
             * 1、参数配置中的header、query、cookie等
             */
            const paramsValidation = validateToolParams(
                toolParams?.toolParams || [],
                parameters
            );
            if (paramsValidation) {
                throw new ApiParamsValidationError(paramsValidation);
            }
            const newTool = {
                toolId: id,
                mcpServerId,
                ...rest,
                toolConf: {
                    ...toolConf,
                    scriptSource: serverSourceType === 'script' ? 'cmd' : undefined,
                    openapiConf: toolConf?.openapiConf
                        ? {
                            ...otherOpenapiConf,
                            parameters: {
                                ...otherParameters,
                            },
                            requestBody: newRequestBody,
                        }
                        : undefined,
                    serverSourceType,
                },
                toolParams: {
                    ...toolParams,
                    toolParams: toolParams.toolParams.map((item: BaseParam) => ({
                        ...item,
                        dataType: item.dataType || item.type,
                    })),
                },
            };
            return newTool;
        },
        [activeToolIndex, mcpServerId, validateFields]
    );
    const handleSubmit = useCallback(
        async () => {
            try {
                on();
                const newTool = await bundleParams();
                if (newTool.toolId) {
                    try {
                        await apiPutMCPTool(newTool);
                        loadMCPServerToolItem({toolId: newTool.toolId, mcpServerId: newTool.mcpServerId});
                        message.success('保存成功');
                        onSuccess?.();
                        setFieldValue(['tools', activeToolIndex, 'toolStatus'], 'complete');
                        off();
                        resetTouchedBasePath();
                    } catch (e) {
                        off();
                        message.error('保存失败');
                        console.error(e);
                    }
                } else {
                    try {
                        const tool = await apiPostMCPTool({
                            toolStatus: 'complete',
                            ...newTool,
                        });
                        setFieldValue(['tools', activeToolIndex, 'id'], tool.id);
                        setFieldValue(['tools', activeToolIndex, 'toolStatus'], tool.toolStatus);
                        message.success('保存成功');
                        onSuccess?.();
                        off();
                        resetTouchedBasePath();
                    } catch (e) {
                        off();
                        message.error('保存失败');
                        console.error(e);
                    }
                }
            } catch (e) {
                console.error(e);
                handleValidationError(e);
                off();
            }
        },
        [activeToolIndex, bundleParams, handleValidationError, off, on, onSuccess, setFieldValue]
    );
    return handleSubmit;
};
