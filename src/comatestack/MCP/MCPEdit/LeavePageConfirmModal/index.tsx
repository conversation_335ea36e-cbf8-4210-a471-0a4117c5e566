import {Modal} from '@panda-design/components';
import {useBoolean} from 'huse';
import {IconAlert} from '@/icons/mcp';
import Footer from './Footer';
import {Content} from './styles';
import {invalidFormInfo, notSavedFormInfo} from './utils';

interface Props {
    nextLocation: string;
    onCancel: () => void;
    type: 'notSaved' | 'invalidForm';
    resetForm: () => void;
    onConfirmLeave?: () => void;
}

const LeavePageConfirmModal = ({onCancel, nextLocation, type, resetForm, onConfirmLeave}: Props) => {
    const [loading, {on, off}] = useBoolean();

    return (
        <Modal
            open
            onCancel={onCancel}
            width={loading ? 500 : 450}
            closable={false}
            footer={
                <Footer
                    nextLocation={nextLocation}
                    onCancel={onCancel}
                    type={type}
                    resetForm={resetForm}
                    onConfirmLeave={onConfirmLeave}
                    loading={loading}
                    onLoadingChange={{on, off}}
                />
            }
        >
            <Content gap={8}>
                <IconAlert />
                <span>
                    {type === 'notSaved' ? notSavedFormInfo : invalidFormInfo}
                </span>
            </Content>
        </Modal>
    );
};

export default LeavePageConfirmModal;
