import {Flex} from 'antd';
import {Checkbox} from 'antd';
import {useState} from 'react';
import {Button} from '@panda-design/components';
import {useSaveLogic, useLeaveLogic} from './hooks';
import {createHandlerWithNotConfirm} from './utils';

interface Props {
    nextLocation: string;
    onCancel: () => void;
    type: 'notSaved' | 'invalidForm';
    resetForm: () => void;
    onConfirmLeave?: () => void;
    loading: boolean;
    onLoadingChange: {
        on: () => void;
        off: () => void;
    };
}

const Footer = ({onCancel, nextLocation, type, resetForm, onConfirmLeave, loading, onLoadingChange}: Props) => {
    const [checked, setChecked] = useState(false);
    const {on, off} = onLoadingChange;

    const save = useSaveLogic({on, off, nextLocation, onConfirmLeave});
    const handleLeave = useLeaveLogic({loading, checked, resetForm, onConfirmLeave, nextLocation});

    const handleCancel = createHandlerWithNotConfirm(onCancel, checked);
    const handleSave = createHandlerWithNotConfirm(save, checked);

    return (
        <Flex align="center" justify="space-between" gap={8}>
            {type === 'notSaved' && (
                <Checkbox checked={checked} onChange={e => setChecked(e.target.checked)}>
                    不再提示
                </Checkbox>
            )}
            <Button onClick={handleCancel} loading={loading}>取消</Button>
            {type === 'notSaved' && (
                <Button type="primary" onClick={handleSave} loading={loading}>
                    保存并离开
                </Button>
            )}
            <Button type="primary" onClick={handleLeave} loading={loading}>
                继续离开
            </Button>
        </Flex>
    );
};

export default Footer;
