import {Form} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useSystemVars, useAuthTypeHandler} from '../../shared/hooks';
import {SERVER_INFO_PATH, SERVER_PARAMS_PATH, SERVER_STATUS_PATH} from '../../shared/constants';
import EnglishIdentifierField from '../../MCPCreate/EnglishIdentifierField';
import GlobalVariableField from '../../MCPCreate/GlobalVariableField';
import MCPIdentifierField from '../../MCPCreate/MCPIdentifierField';
import ServiceNameField from '../../MCPCreate/ServiceNameField';
import AuthDescription from '../../MCPCreate/OpenApiFields/AuthDescriptionField';
import AuthTypeField from '../../MCPCreate/OpenApiFields/AuthTypeField';
import DescriptionField from './DescriptionField';
import SceneField from './SceneField';
import ProtocolField from './ProtocolField';
import OverviewField from './OverviewField';
import ServerConfigFieldWidthWrapper from './ServerConfigFieldWidthWrapper';
import {ContentWrapper, LeftContent, RightContent} from './styles';

const BasicInfoContent = () => {
    const serverSourceType = Form.useWatch('serverSourceType');
    const form = Form.useFormInstance();
    const authType = useWatch(['serverInfo', 'serverConf', 'serverExtension', 'serverAuthType'], form);

    const systemVars = useSystemVars({
        mode: serverSourceType,
        authType,
    });

    const handleAuthTypeChange = useAuthTypeHandler({
        form,
        serverParamsPath: ['serverInfo', 'serverParams'],
    });

    return (
        // style={{display: hidden ? 'none' : 'block'}}
        <div>
            <ContentWrapper>
                <LeftContent>
                    <Form.Item name={SERVER_STATUS_PATH} hidden />
                    <ServiceNameField path={SERVER_INFO_PATH} />
                    <EnglishIdentifierField disabled path={SERVER_INFO_PATH} />
                    <MCPIdentifierField path={SERVER_INFO_PATH} />
                    <DescriptionField path={SERVER_INFO_PATH} />
                    <SceneField path={SERVER_INFO_PATH} />
                    <ProtocolField path={SERVER_INFO_PATH} disabled />
                    {serverSourceType === 'openapi' && (
                        <>
                            <AuthTypeField path={SERVER_INFO_PATH} onAuthTypeChange={handleAuthTypeChange} />
                            <AuthDescription path={SERVER_INFO_PATH} />
                        </>
                    )}
                    <Form.Item label="全局变量" name={SERVER_PARAMS_PATH}>
                        <GlobalVariableField
                            path={SERVER_PARAMS_PATH}
                            rowKey="index"
                            systemVars={systemVars}
                        />
                    </Form.Item>
                </LeftContent>
                <RightContent>
                    {
                        serverSourceType === 'external' && (
                            <ServerConfigFieldWidthWrapper path={SERVER_INFO_PATH} />
                        )
                    }
                    <OverviewField />
                </RightContent>
            </ContentWrapper>
        </div>
    );
};

export default BasicInfoContent;
