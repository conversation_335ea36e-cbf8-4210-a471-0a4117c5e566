export const SYSTEM_VARS_TEMPLATE = [
    {
        name: 'AccessKey',
        description: '请求时用于生成签名',
        dataType: 'string',
        required: true,
        isSystemVar: true,
    },
    {
        name: 'SecretKey',
        description: '请求时用于生成签名',
        dataType: 'string',
        required: true,
        isSystemVar: true,
    },
];
/**
 * 如果path不以常量的形式定义在外部，每次控件渲染时都会收到一个新的数组引用。
 * 如果控件内部使用path作为effect的依赖项，会导致effect每次都执行。
 */
export const SERVER_INFO_PATH = ['serverInfo'];
export const SERVER_PARAMS_PATH = [...SERVER_INFO_PATH, 'serverParams'];
export const SERVER_STATUS_PATH = [...SERVER_INFO_PATH, 'serverStatus'];
