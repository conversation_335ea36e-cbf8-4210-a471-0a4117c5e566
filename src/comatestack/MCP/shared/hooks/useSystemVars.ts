import {useMemo} from 'react';
import {SYSTEM_VARS_TEMPLATE} from '../constants';

interface UseSystemVarsProps {
    mode: string;
    authType: string;
}

export const useSystemVars = ({mode, authType}: UseSystemVarsProps) => {
    return useMemo(
        () => {
            if (mode === 'openapi' && authType === 'CLOUD_INIT_IAM') {
                return SYSTEM_VARS_TEMPLATE;
            }
            return [];
        },
        [mode, authType]
    );
};
