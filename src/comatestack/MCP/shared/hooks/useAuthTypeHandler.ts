import {useCallback} from 'react';
import {MCPServerAuthType} from '@/types/mcp/mcp';
import {SYSTEM_VARS_TEMPLATE} from '../constants';

interface UseAuthTypeHandlerProps {
    form: any;
    serverParamsPath: string[];
}

export const useAuthTypeHandler = ({form, serverParamsPath}: UseAuthTypeHandlerProps) => {
    return useCallback(
        (newAuthType: MCPServerAuthType) => {
            const currentServerParams = form.getFieldValue(serverParamsPath) || [];

            if (newAuthType === 'CLOUD_INIT_IAM') {
                const processedVars: any[] = [];
                const remainingUserVars: any[] = [];

                SYSTEM_VARS_TEMPLATE.forEach(systemVar => {
                    const existingVar = currentServerParams.find((param: any) => param.name === systemVar.name);
                    if (existingVar) {
                        processedVars.push({
                            ...existingVar,
                            description: systemVar.description,
                            dataType: systemVar.dataType,
                            required: systemVar.required,
                            isSystemVar: true,
                        });
                    } else {
                        processedVars.push(systemVar);
                    }
                });

                currentServerParams.forEach((param: any) => {
                    if (!param.isSystemVar && !SYSTEM_VARS_TEMPLATE.some(sys => sys.name === param.name)) {
                        remainingUserVars.push(param);
                    }
                });

                const combinedVars = [...processedVars, ...remainingUserVars];
                form.setFieldValue(serverParamsPath, combinedVars);
            } else {
                const filteredParams = currentServerParams.filter((param: any) => !param.isSystemVar);
                form.setFieldValue(serverParamsPath, filteredParams);
            }
        },
        [form, serverParamsPath]
    );
};
