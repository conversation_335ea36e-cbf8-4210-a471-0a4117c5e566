import {useEffect} from 'react';
import {trackMCPPageView, trackPageView} from '@/utils/track';
import {apiPostUV} from '@/api/iplayground/metrics';
import {APP_NAME} from '@/constants/app';

interface TrackPageViewProps {
    scope: string;
}

export const TrackPageView = ({scope}: TrackPageViewProps): null => {
    useEffect(
        () => {
            if (APP_NAME === 'mcp') {
                trackMCPPageView(scope);
            }
            else {
                trackPageView(scope);
            }
            if (APP_NAME === 'iplayground') {
                apiPostUV({page: scope});
            }

        },
        [scope]
    );
    return null;
};
